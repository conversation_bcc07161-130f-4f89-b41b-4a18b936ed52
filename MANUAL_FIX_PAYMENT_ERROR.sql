-- =====================================================
-- MANUAL FIX FOR PAYMENT DATABASE ERROR
-- =====================================================
-- Error: Unknown column 'اسم_العميل' in 'field list'
-- 
-- This error occurs because database triggers are trying 
-- to update columns that don't exist in the target tables.
-- =====================================================

USE project_manager_V2;

-- Step 1: Check current problematic triggers
SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE
FROM INFORMATION_SCHEMA.TRIGGERS
WHERE TRIGGER_SCHEMA = 'project_manager_V2'
AND TRIGGER_NAME IN ('تحديث_التصميم', 'update_custody_project_info');

-- Step 2: Remove the problematic triggers
-- These triggers are trying to update non-existent columns

-- Remove the design update trigger
DROP TRIGGER IF EXISTS تحديث_التصميم;

-- Remove the custody project info update trigger  
DROP TRIGGER IF EXISTS update_custody_project_info;

-- Step 3: Verify the triggers are removed
SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE
FROM INFORMATION_SCHEMA.TRIGGERS
WHERE TRIGGER_SCHEMA = 'project_manager_V2'
AND TRIGGER_NAME IN ('تحديث_التصميم', 'update_custody_project_info');

-- Step 4: Test payment insertion
-- This should now work without the column reference error
INSERT INTO المشاريع_المدفوعات 
(معرف_العميل, معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع, طريقة_الدفع, خصم, المستلم, المستخدم) 
VALUES (1, 1, 100.00, 'test payment', CURDATE(), 'نقدي', 0, 'test user', 'النظام');

-- Get the ID of the test payment
SET @test_payment_id = LAST_INSERT_ID();

-- Verify the payment was inserted
SELECT * FROM المشاريع_المدفوعات WHERE id = @test_payment_id;

-- Clean up the test payment
DELETE FROM المشاريع_المدفوعات WHERE id = @test_payment_id;

-- Step 5: Verify that the essential payment triggers still exist
-- These triggers should remain to update project totals
SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE
FROM INFORMATION_SCHEMA.TRIGGERS
WHERE TRIGGER_SCHEMA = 'project_manager_V2'
AND EVENT_OBJECT_TABLE = 'المشاريع_المدفوعات';

-- =====================================================
-- EXPLANATION OF THE FIX
-- =====================================================
-- 
-- PROBLEM:
-- The triggers 'تحديث_التصميم' and 'update_custody_project_info' 
-- were trying to update columns 'اسم_العميل' and 'اسم_المشروع' 
-- in tables that don't have these columns:
-- 
-- - المشاريع_المراحل table does NOT have 'اسم_العميل' column
-- - المقاولات_العهد table does NOT have 'اسم_العميل' column
-- 
-- SOLUTION:
-- Remove these problematic triggers since they reference 
-- non-existent columns. The payment system will work 
-- perfectly without them.
-- 
-- WHAT REMAINS WORKING:
-- - Payment insertion/update/deletion
-- - Automatic project total updates (via other triggers)
-- - All payment management functionality
-- - Database integrity and relationships
-- 
-- WHAT WAS REMOVED:
-- - Automatic client name updates in project phases
-- - Automatic client name updates in custody records
-- (These were not essential for payment functionality)
-- 
-- =====================================================

-- Optional: If you want to recreate the triggers without the problematic columns
-- (Only run these if the tables actually have the required columns)

/*
-- Recreate design trigger without اسم_العميل (only if اسم_المشروع column exists)
DELIMITER $$
CREATE TRIGGER تحديث_التصميم
AFTER UPDATE ON المشاريع
FOR EACH ROW
BEGIN
    -- Only update اسم_المشروع if the column exists
    UPDATE المشاريع_المراحل
    SET اسم_المشروع = NEW.اسم_المشروع
    WHERE معرف_المشروع = NEW.id;
END$$
DELIMITER ;

-- Recreate custody trigger without اسم_العميل (only if اسم_المشروع column exists)
DELIMITER $$
CREATE TRIGGER update_custody_project_info
AFTER UPDATE ON المشاريع
FOR EACH ROW
BEGIN
    -- Only update اسم_المشروع if the column exists
    UPDATE المقاولات_العهد
    SET اسم_المشروع = NEW.اسم_المشروع
    WHERE معرف_المشروع = NEW.id;
END$$
DELIMITER ;
*/

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check table structures to confirm which columns exist
DESCRIBE المشاريع_المدفوعات;
DESCRIBE المشاريع_المراحل;
DESCRIBE المقاولات_العهد;

-- Check remaining triggers
SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT
FROM INFORMATION_SCHEMA.TRIGGERS
WHERE TRIGGER_SCHEMA = 'project_manager_V2'
ORDER BY TRIGGER_NAME;

-- =====================================================
-- SUCCESS CONFIRMATION
-- =====================================================
-- After running this script:
-- 1. Payment saving should work without errors
-- 2. Project totals will still update automatically
-- 3. All payment management features will function normally
-- 4. No data will be lost
-- 5. Database integrity is maintained
-- =====================================================
