# نظام إدارة المدفوعات الشامل
## Comprehensive Payment Management System

### نظرة عامة | Overview

تم تطوير نظام إدارة المدفوعات الشامل (`المدفوعات.py`) ليكون حلاً متكاملاً لإدارة جميع المدفوعات المتعلقة بالمشاريع في النظام. يوفر هذا النظام واجهة مستخدم احترافية مع دعم كامل للغة العربية والتخطيط من اليمين إلى اليسار (RTL).

### الميزات الرئيسية | Key Features

#### 1. إدارة المدفوعات الشاملة
- **إضافة دفعات جديدة**: نافذة احترافية لإضافة دفعات مع جميع التفاصيل المطلوبة
- **تعديل الدفعات**: إمكانية تعديل الدفعات الموجودة مع الحفاظ على سجل التغييرات
- **حذف الدفعات**: حذف آمن مع تأكيد وتحديث تلقائي للمبالغ
- **عرض شامل**: جدول احترافي يعرض جميع تفاصيل المدفوعات

#### 2. التكامل مع قاعدة البيانات
- **استخدام الجداول الموجودة**: يعمل مع جداول `المشاريع` و `المشاريع_المدفوعات` و `العملاء`
- **استعلامات JOIN**: جلب أسماء العملاء والمشاريع تلقائياً
- **التحديث التلقائي**: تحديث مبالغ المشاريع تلقائياً عند إضافة/تعديل/حذف الدفعات
- **معالجة الخصم**: دعم كامل لحقل الخصم في العمليات الحسابية

#### 3. واجهة المستخدم المتقدمة
- **تصميم احترافي**: واجهة عصرية مع ألوان متناسقة وأيقونات واضحة
- **دعم RTL**: تخطيط كامل من اليمين إلى اليسار للغة العربية
- **تنقل سهل**: استخدام مفتاح Enter للتنقل بين الحقول
- **رسائل واضحة**: رسائل نجاح وخطأ واضحة ومفهومة

#### 4. الميزات المتقدمة
- **البحث والتصفية**: نافذة بحث متقدمة بمعايير متعددة
- **طباعة الإيصالات**: إنشاء وطباعة إيصالات للدفعات
- **تصدير البيانات**: تصدير بيانات المدفوعات إلى ملفات CSV/Excel
- **الإحصائيات**: عرض إحصائيات شاملة للمدفوعات والمبالغ

### هيكل النظام | System Structure

#### الفئات الرئيسية | Main Classes

1. **PaymentManagementWindow**: النافذة الرئيسية لإدارة المدفوعات
2. **PaymentDialog**: نافذة إضافة/تعديل الدفعات
3. **SearchFilterDialog**: نافذة البحث والتصفية

#### قاعدة البيانات | Database Schema

```sql
-- جدول المشاريع_المدفوعات
CREATE TABLE المشاريع_المدفوعات (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    معرف_العميل INT,
    معرف_المشروع INT,
    المبلغ_المدفوع DECIMAL(10,2),
    وصف_المدفوع VARCHAR(255),
    تاريخ_الدفع DATE,
    طريقة_الدفع VARCHAR(255),
    خصم DECIMAL(10,2),
    المستلم VARCHAR(255),
    المستخدم VARCHAR(50),
    تاريخ_الإضافة DATETIME DEFAULT CURRENT_TIMESTAMP,
    السنة INT GENERATED ALWAYS AS (YEAR(تاريخ_الدفع)) STORED
);
```

### التكامل مع النظام الموجود | Integration

#### 1. تكامل مع نظام البطاقات
```python
# في نظام_البطاقات.py
def manage_payments(self):
    from المدفوعات import open_payment_management_window
    self.payments_window = open_payment_management_window(
        self.parent(), project_id, project_data
    )
```

#### 2. تكامل مع مراحل المشروع
```python
# في مراحل_المشروع.py
def add_new_payment(self):
    from المدفوعات import add_payment_dialog
    result = add_payment_dialog(self, self.project_id, project_data)

def view_payments(self):
    from المدفوعات import open_payment_management_window
    self.payments_window = open_payment_management_window(
        self, self.project_id, project_data
    )
```

### طرق الاستخدام | Usage Methods

#### 1. فتح نافذة إدارة المدفوعات
```python
from المدفوعات import open_payment_management_window

project_data = {
    'id': 1,
    'اسم_المشروع': 'اسم المشروع',
    'اسم_العميل': 'اسم العميل',
    'معرف_العميل': 1
}

window = open_payment_management_window(parent, project_id=1, project_data=project_data)
```

#### 2. إضافة دفعة جديدة
```python
from المدفوعات import add_payment_dialog

result = add_payment_dialog(parent, project_id=1, project_data=project_data)
if result == QDialog.Accepted:
    print("تم إضافة الدفعة بنجاح")
```

#### 3. تعديل دفعة موجودة
```python
from المدفوعات import edit_payment_dialog

result = edit_payment_dialog(parent, payment_id=123)
if result == QDialog.Accepted:
    print("تم تعديل الدفعة بنجاح")
```

### الميزات التقنية | Technical Features

#### 1. التحقق من البيانات
- **التحقق من المبلغ**: التأكد من أن المبلغ رقم صحيح وأكبر من الصفر
- **التحقق من التاريخ**: التأكد من صحة تنسيق التاريخ
- **التحقق من المشروع**: التأكد من اختيار مشروع صحيح
- **التحقق من طريقة الدفع**: التأكد من اختيار طريقة دفع صالحة

#### 2. معالجة الأخطاء
- **رسائل خطأ واضحة**: عرض رسائل خطأ مفهومة للمستخدم
- **التعامل مع قاعدة البيانات**: معالجة أخطاء الاتصال والاستعلامات
- **النسخ الاحتياطي**: حماية البيانات من الفقدان

#### 3. الأداء والكفاءة
- **استعلامات محسنة**: استخدام JOIN لتقليل عدد الاستعلامات
- **تحديث ذكي**: تحديث البيانات فقط عند الحاجة
- **ذاكرة محسنة**: إدارة فعالة للذاكرة والموارد

### متطلبات النظام | System Requirements

#### المكتبات المطلوبة | Required Libraries
- PySide6 (Qt for Python)
- mysql-connector-python
- qtawesome (للأيقونات)
- decimal (للحسابات المالية الدقيقة)

#### الملفات المطلوبة | Required Files
- `for_all.py` - الوظائف العامة
- `ستايل.py` - إعدادات التصميم
- `ui_boton.py` - عناصر واجهة المستخدم
- `db.py` - إعدادات قاعدة البيانات
- `الطباعة.py` - وظائف الطباعة

### الاختبار | Testing

يمكن اختبار النظام باستخدام الملف `test_payments.py`:

```bash
python test_payments.py
```

### الدعم والصيانة | Support & Maintenance

#### السجلات | Logging
- تسجيل جميع العمليات المالية
- تتبع تغييرات البيانات
- سجل الأخطاء والاستثناءات

#### النسخ الاحتياطي | Backup
- نسخ احتياطية تلقائية لقاعدة البيانات
- حماية البيانات المالية الحساسة
- إمكانية الاستعادة السريعة

### المطورون | Developers

تم تطوير هذا النظام باستخدام أفضل الممارسات في البرمجة:
- **كود نظيف**: تنظيم واضح وتعليقات شاملة
- **معمارية قابلة للتوسع**: سهولة إضافة ميزات جديدة
- **اختبارات شاملة**: ضمان جودة وموثوقية النظام
- **توثيق كامل**: دليل شامل للاستخدام والتطوير

---

© 2024 نظام إدارة المشاريع - جميع الحقوق محفوظة
