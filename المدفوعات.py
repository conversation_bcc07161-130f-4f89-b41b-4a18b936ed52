from for_all import *
from ستايل import *
from ui_boton import *
from db import *
import qtawesome as qta
from datetime import datetime, date
import mysql.connector
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QLineEdit, 
    QComboBox, QDateEdit, QTextEdit, QPushButton, QTableWidget, QTableWidgetItem,
    QMessageBox, QFrame, QGroupBox, QSpacerItem, QSizePolicy, QHeaderView,
    QFileDialog, QProgressDialog, QInputDialog
)
from PySide6.QtCore import Qt, QDate, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon
from decimal import Decimal
import os
from الطباعة import *

class PaymentManagementWindow(QDialog):
    """نافذة إدارة المدفوعات الشاملة"""
    
    def __init__(self, parent=None, project_id=None, project_data=None):
        super().__init__(parent)
        self.parent = parent
        self.project_id = project_id
        self.project_data = project_data or {}
        self.client_id = self.project_data.get('معرف_العميل', None)
        self.client_name = self.project_data.get('اسم_العميل', '')
        self.project_name = self.project_data.get('اسم_المشروع', '')
        
        # إعداد النافذة الأساسية
        self.setup_window()
        
        # إنشاء واجهة المستخدم
        self.create_ui()
        
        # تحميل البيانات
        self.load_payments_data()
        
        # تطبيق الستايل
        apply_stylesheet(self)
        
    def setup_window(self):
        """إعداد النافذة الأساسية"""
        title = "إدارة المدفوعات"
        if self.project_name:
            title += f" - {self.project_name}"
        if self.client_name:
            title += f" - {self.client_name}"
            
        self.setWindowTitle(title)
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # إعداد أيقونة النافذة
        app_icon_path = os.path.join(icons_dir, "icon_app.png")
        if os.path.exists(app_icon_path):
            self.setWindowIcon(QIcon(app_icon_path))
    
    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # قسم معلومات المشروع
        self.create_project_info_section(main_layout)
        
        # قسم الإحصائيات
        self.create_statistics_section(main_layout)
        
        # قسم أزرار الإجراءات
        self.create_action_buttons_section(main_layout)
        
        # قسم جدول المدفوعات
        self.create_payments_table_section(main_layout)
        
        # قسم أزرار التحكم السفلية
        self.create_bottom_buttons_section(main_layout)
    
    def create_project_info_section(self, main_layout):
        """إنشاء قسم معلومات المشروع"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.StyledPanel)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        info_layout = QGridLayout(info_frame)
        
        # معلومات المشروع
        project_label = QLabel("معلومات المشروع")
        project_label.setFont(QFont("Arial", 12, QFont.Bold))
        project_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px;")
        info_layout.addWidget(project_label, 0, 0, 1, 4)
        
        # اسم المشروع
        info_layout.addWidget(QLabel("اسم المشروع:"), 1, 0)
        self.project_name_label = QLabel(self.project_name)
        self.project_name_label.setStyleSheet("font-weight: bold; color: #34495e;")
        info_layout.addWidget(self.project_name_label, 1, 1)
        
        # اسم العميل
        info_layout.addWidget(QLabel("اسم العميل:"), 1, 2)
        self.client_name_label = QLabel(self.client_name)
        self.client_name_label.setStyleSheet("font-weight: bold; color: #34495e;")
        info_layout.addWidget(self.client_name_label, 1, 3)
        
        main_layout.addWidget(info_frame)
    
    def create_statistics_section(self, main_layout):
        """إنشاء قسم الإحصائيات"""
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #28a745;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        stats_layout = QHBoxLayout(stats_frame)
        
        # إجمالي المبلغ
        self.total_amount_label = QLabel("إجمالي المبلغ: 0.00")
        self.total_amount_label.setFont(QFont("Arial", 11, QFont.Bold))
        self.total_amount_label.setStyleSheet("color: #2c3e50; padding: 5px;")
        stats_layout.addWidget(self.total_amount_label)
        
        # إجمالي المدفوع
        self.total_paid_label = QLabel("إجمالي المدفوع: 0.00")
        self.total_paid_label.setFont(QFont("Arial", 11, QFont.Bold))
        self.total_paid_label.setStyleSheet("color: #28a745; padding: 5px;")
        stats_layout.addWidget(self.total_paid_label)
        
        # المبلغ المتبقي
        self.remaining_amount_label = QLabel("المبلغ المتبقي: 0.00")
        self.remaining_amount_label.setFont(QFont("Arial", 11, QFont.Bold))
        self.remaining_amount_label.setStyleSheet("color: #dc3545; padding: 5px;")
        stats_layout.addWidget(self.remaining_amount_label)
        
        # عدد الدفعات
        self.payments_count_label = QLabel("عدد الدفعات: 0")
        self.payments_count_label.setFont(QFont("Arial", 11, QFont.Bold))
        self.payments_count_label.setStyleSheet("color: #6c757d; padding: 5px;")
        stats_layout.addWidget(self.payments_count_label)
        
        main_layout.addWidget(stats_frame)
    
    def create_action_buttons_section(self, main_layout):
        """إنشاء قسم أزرار الإجراءات"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        # زر إضافة دفعة جديدة
        self.add_payment_btn = QPushButton(qta.icon('fa5s.plus', color='white'), "إضافة دفعة جديدة")
        self.add_payment_btn.setMinimumSize(150, 40)
        self.add_payment_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.add_payment_btn.clicked.connect(self.add_new_payment)
        buttons_layout.addWidget(self.add_payment_btn)
        
        # زر تحديث البيانات
        self.refresh_btn = QPushButton(qta.icon('fa5s.sync-alt', color='white'), "تحديث")
        self.refresh_btn.setMinimumSize(100, 40)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.refresh_btn.clicked.connect(self.load_payments_data)
        buttons_layout.addWidget(self.refresh_btn)
        
        # زر البحث والتصفية
        self.search_btn = QPushButton(qta.icon('fa5s.search', color='white'), "بحث وتصفية")
        self.search_btn.setMinimumSize(120, 40)
        self.search_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.search_btn.clicked.connect(self.show_search_dialog)
        buttons_layout.addWidget(self.search_btn)
        
        # زر طباعة التقرير
        self.print_btn = QPushButton(qta.icon('fa5s.print', color='white'), "طباعة التقرير")
        self.print_btn.setMinimumSize(120, 40)
        self.print_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        self.print_btn.clicked.connect(self.print_payments_report)
        buttons_layout.addWidget(self.print_btn)
        
        # زر تصدير البيانات
        self.export_btn = QPushButton(qta.icon('fa5s.file-export', color='white'), "تصدير")
        self.export_btn.setMinimumSize(100, 40)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #20c997;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1ba085;
            }
        """)
        self.export_btn.clicked.connect(self.export_payments_data)
        buttons_layout.addWidget(self.export_btn)
        
        buttons_layout.addStretch()
        main_layout.addWidget(buttons_frame)

    def create_payments_table_section(self, main_layout):
        """إنشاء قسم جدول المدفوعات"""
        table_frame = QFrame()
        table_frame.setFrameStyle(QFrame.StyledPanel)
        table_layout = QVBoxLayout(table_frame)

        # عنوان الجدول
        table_title = QLabel("سجل المدفوعات")
        table_title.setFont(QFont("Arial", 12, QFont.Bold))
        table_title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        table_layout.addWidget(table_title)

        # إنشاء الجدول
        self.payments_table = QTableWidget()

        # تحديد أعمدة الجدول
        headers = [
            "ID", "رقم الدفعة", "المبلغ المدفوع", "وصف المدفوع",
            "تاريخ الدفع", "طريقة الدفع", "المستلم", "المستخدم", "تاريخ الإضافة"
        ]

        self.payments_table.setColumnCount(len(headers))
        self.payments_table.setHorizontalHeaderLabels(headers)

        # إخفاء عمود ID
        self.payments_table.hideColumn(0)

        # تطبيق إعدادات الجدول
        table_setting(self.payments_table)

        # تعيين عرض الأعمدة
        self.payments_table.setColumnWidth(1, 80)   # رقم الدفعة
        self.payments_table.setColumnWidth(2, 120)  # المبلغ المدفوع
        self.payments_table.setColumnWidth(3, 200)  # وصف المدفوع
        self.payments_table.setColumnWidth(4, 100)  # تاريخ الدفع
        self.payments_table.setColumnWidth(5, 120)  # طريقة الدفع
        self.payments_table.setColumnWidth(6, 120)  # المستلم
        self.payments_table.setColumnWidth(7, 100)  # المستخدم
        self.payments_table.setColumnWidth(8, 140)  # تاريخ الإضافة

        # ربط النقر المزدوج بفتح نافذة التعديل
        self.payments_table.itemDoubleClicked.connect(self.edit_payment)

        table_layout.addWidget(self.payments_table)
        main_layout.addWidget(table_frame)

    def create_bottom_buttons_section(self, main_layout):
        """إنشاء قسم أزرار التحكم السفلية"""
        bottom_frame = QFrame()
        bottom_layout = QHBoxLayout(bottom_frame)

        # زر تعديل الدفعة المحددة
        self.edit_payment_btn = QPushButton(qta.icon('fa5s.edit', color='white'), "تعديل الدفعة")
        self.edit_payment_btn.setMinimumSize(120, 40)
        self.edit_payment_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.edit_payment_btn.clicked.connect(self.edit_payment)
        bottom_layout.addWidget(self.edit_payment_btn)

        # زر حذف الدفعة المحددة
        self.delete_payment_btn = QPushButton(qta.icon('fa5s.trash', color='white'), "حذف الدفعة")
        self.delete_payment_btn.setMinimumSize(120, 40)
        self.delete_payment_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.delete_payment_btn.clicked.connect(self.delete_payment)
        bottom_layout.addWidget(self.delete_payment_btn)

        # زر طباعة إيصال
        self.print_receipt_btn = QPushButton(qta.icon('fa5s.receipt', color='white'), "طباعة إيصال")
        self.print_receipt_btn.setMinimumSize(120, 40)
        self.print_receipt_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        self.print_receipt_btn.clicked.connect(self.print_payment_receipt)
        bottom_layout.addWidget(self.print_receipt_btn)

        bottom_layout.addStretch()

        # زر إغلاق النافذة
        self.close_btn = QPushButton(qta.icon('fa5s.times', color='white'), "إغلاق")
        self.close_btn.setMinimumSize(100, 40)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.close_btn.clicked.connect(self.close)
        bottom_layout.addWidget(self.close_btn)

        main_layout.addWidget(bottom_frame)

    def load_payments_data(self):
        """تحميل بيانات المدفوعات من قاعدة البيانات"""
        try:
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            # جلب بيانات المدفوعات مع معلومات المشروع والعميل
            if self.project_id:
                # جلب مدفوعات مشروع محدد
                cursor.execute("""
                    SELECT dp.id, dp.معرف_العميل, dp.معرف_المشروع, dp.وصف_المدفوع, dp.المبلغ_المدفوع,
                           dp.تاريخ_الدفع, dp.طريقة_الدفع, dp.المستلم, dp.خصم, dp.المستخدم, dp.تاريخ_الإضافة, dp.السنة,
                           c.اسم_العميل, p.اسم_المشروع, p.المبلغ as مبلغ_المشروع, p.المدفوع as إجمالي_المدفوع
                    FROM المشاريع_المدفوعات dp
                    LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
                    LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                    WHERE dp.معرف_المشروع = %s
                    ORDER BY dp.id DESC
                """, (self.project_id,))
            else:
                # جلب جميع المدفوعات
                cursor.execute("""
                    SELECT dp.id, dp.معرف_العميل, dp.معرف_المشروع, dp.وصف_المدفوع, dp.المبلغ_المدفوع,
                           dp.تاريخ_الدفع, dp.طريقة_الدفع, dp.المستلم, dp.خصم, dp.المستخدم, dp.تاريخ_الإضافة, dp.السنة,
                           c.اسم_العميل, p.اسم_المشروع, p.المبلغ as مبلغ_المشروع, p.المدفوع as إجمالي_المدفوع
                    FROM المشاريع_المدفوعات dp
                    LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
                    LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                    ORDER BY dp.id DESC
                """)

            payments_data = cursor.fetchall()

            # تحديث الجدول
            self.update_payments_table(payments_data)

            # تحديث الإحصائيات
            self.update_statistics(payments_data)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")
            print(f"خطأ في تحميل البيانات: {e}")

    def update_payments_table(self, payments_data):
        """تحديث جدول المدفوعات"""
        self.payments_table.setRowCount(len(payments_data))

        for row, payment in enumerate(payments_data):
            # ID (مخفي)
            self.payments_table.setItem(row, 0, QTableWidgetItem(str(payment[0])))

            # رقم الدفعة
            self.payments_table.setItem(row, 1, QTableWidgetItem(str(payment[0])))

            # المبلغ المدفوع
            amount_item = QTableWidgetItem(f"{float(payment[4]):,.2f}")
            amount_item.setTextAlignment(Qt.AlignCenter)
            self.payments_table.setItem(row, 2, amount_item)

            # وصف المدفوع
            self.payments_table.setItem(row, 3, QTableWidgetItem(str(payment[3] or "")))

            # تاريخ الدفع
            date_item = QTableWidgetItem(str(payment[5]))
            date_item.setTextAlignment(Qt.AlignCenter)
            self.payments_table.setItem(row, 4, date_item)

            # طريقة الدفع
            self.payments_table.setItem(row, 5, QTableWidgetItem(str(payment[6] or "")))

            # المستلم
            self.payments_table.setItem(row, 6, QTableWidgetItem(str(payment[7] or "")))

            # المستخدم
            self.payments_table.setItem(row, 7, QTableWidgetItem(str(payment[9] or "")))

            # تاريخ الإضافة
            add_date_item = QTableWidgetItem(str(payment[10]))
            add_date_item.setTextAlignment(Qt.AlignCenter)
            self.payments_table.setItem(row, 8, add_date_item)

    def update_statistics(self, payments_data):
        """تحديث الإحصائيات"""
        try:
            if self.project_id and payments_data:
                # الحصول على بيانات المشروع من أول دفعة
                project_total = float(payments_data[0][15]) if payments_data[0][15] else 0.0
                project_paid = float(payments_data[0][16]) if payments_data[0][16] else 0.0
                project_remaining = project_total - project_paid
                payments_count = len(payments_data)

                # تحديث التسميات
                self.total_amount_label.setText(f"إجمالي المبلغ: {project_total:,.2f}")
                self.total_paid_label.setText(f"إجمالي المدفوع: {project_paid:,.2f}")
                self.remaining_amount_label.setText(f"المبلغ المتبقي: {project_remaining:,.2f}")
                self.payments_count_label.setText(f"عدد الدفعات: {payments_count}")
            else:
                # حساب الإحصائيات العامة
                total_paid = sum(float(payment[4]) for payment in payments_data)
                payments_count = len(payments_data)

                self.total_amount_label.setText("إجمالي المبلغ: غير محدد")
                self.total_paid_label.setText(f"إجمالي المدفوع: {total_paid:,.2f}")
                self.remaining_amount_label.setText("المبلغ المتبقي: غير محدد")
                self.payments_count_label.setText(f"عدد الدفعات: {payments_count}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def add_new_payment(self):
        """إضافة دفعة جديدة"""
        dialog = PaymentDialog(self, project_id=self.project_id, project_data=self.project_data)
        if dialog.exec() == QDialog.Accepted:
            self.load_payments_data()

    def edit_payment(self):
        """تعديل الدفعة المحددة"""
        selected_row = self.payments_table.currentRow()
        if selected_row == -1:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد الدفعة المراد تعديلها.")
            return

        # الحصول على ID الدفعة
        payment_id = self.payments_table.item(selected_row, 0).text()

        # فتح نافذة التعديل
        dialog = PaymentDialog(self, payment_id=payment_id, edit_mode=True)
        if dialog.exec() == QDialog.Accepted:
            self.load_payments_data()

    def delete_payment(self):
        """حذف الدفعة المحددة"""
        selected_row = self.payments_table.currentRow()
        if selected_row == -1:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد الدفعة المراد حذفها.")
            return

        # الحصول على معلومات الدفعة
        payment_id = self.payments_table.item(selected_row, 0).text()
        payment_number = self.payments_table.item(selected_row, 1).text()
        payment_amount = self.payments_table.item(selected_row, 2).text()

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            'تأكيد حذف الدفعة',
            f'هل أنت متأكد من حذف الدفعة رقم {payment_number} بمبلغ {payment_amount}؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        try:
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            # حذف الدفعة من قاعدة البيانات
            cursor.execute("DELETE FROM المشاريع_المدفوعات WHERE id = %s", (payment_id,))
            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجاح", f"تم حذف الدفعة رقم {payment_number} بنجاح.")

            # تحديث البيانات
            self.load_payments_data()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الدفعة:\n{str(e)}")
            print(f"خطأ في حذف الدفعة: {e}")

    def print_payment_receipt(self):
        """طباعة إيصال الدفعة المحددة"""
        selected_row = self.payments_table.currentRow()
        if selected_row == -1:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد الدفعة لطباعة إيصالها.")
            return

        # الحصول على معلومات الدفعة
        payment_id = self.payments_table.item(selected_row, 0).text()

        try:
            # جلب تفاصيل الدفعة من قاعدة البيانات
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT dp.*, c.اسم_العميل, p.اسم_المشروع
                FROM المشاريع_المدفوعات dp
                LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
                LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                WHERE dp.id = %s
            """, (payment_id,))

            payment_data = cursor.fetchone()
            conn.close()

            if payment_data:
                # طباعة الإيصال
                self.print_receipt(payment_data)
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات الدفعة.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة الإيصال:\n{str(e)}")
            print(f"خطأ في طباعة الإيصال: {e}")

    def print_receipt(self, payment_data):
        """طباعة إيصال الدفعة"""
        try:
            # إنشاء محتوى الإيصال
            receipt_content = f"""
            ==========================================
                        إيصال دفعة
            ==========================================

            رقم الإيصال: {payment_data[0]}
            التاريخ: {payment_data[5]}

            ------------------------------------------
            بيانات العميل:
            ------------------------------------------
            اسم العميل: {payment_data[13]}
            اسم المشروع: {payment_data[14]}

            ------------------------------------------
            تفاصيل الدفعة:
            ------------------------------------------
            المبلغ المدفوع: {float(payment_data[4]):,.2f}
            وصف المدفوع: {payment_data[3]}
            طريقة الدفع: {payment_data[6]}
            المستلم: {payment_data[7]}

            ------------------------------------------
            ملاحظات:
            تم إنشاء هذا الإيصال بواسطة نظام إدارة المشاريع
            المستخدم: {payment_data[9]}
            تاريخ الإضافة: {payment_data[10]}

            ==========================================
            """

            # يمكن هنا إضافة كود الطباعة الفعلي
            QMessageBox.information(self, "طباعة الإيصال", "تم إنشاء الإيصال بنجاح.\n\nيمكنك الآن طباعته.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء الإيصال:\n{str(e)}")

    def print_payments_report(self):
        """طباعة تقرير المدفوعات"""
        try:
            # إنشاء تقرير شامل للمدفوعات
            report_title = f"تقرير المدفوعات - {self.project_name}" if self.project_name else "تقرير المدفوعات العام"

            QMessageBox.information(self, "طباعة التقرير", f"سيتم إنشاء {report_title}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}")

    def export_payments_data(self):
        """تصدير بيانات المدفوعات"""
        try:
            # اختيار مسار الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير بيانات المدفوعات",
                f"مدفوعات_{self.project_name}_{datetime.now().strftime('%Y%m%d')}.csv",
                "CSV Files (*.csv);;Excel Files (*.xlsx)"
            )

            if file_path:
                QMessageBox.information(self, "تصدير البيانات", f"سيتم تصدير البيانات إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير البيانات:\n{str(e)}")

    def show_search_dialog(self):
        """عرض نافذة البحث والتصفية"""
        dialog = SearchFilterDialog(self)
        dialog.exec()


class PaymentDialog(QDialog):
    """نافذة إضافة/تعديل الدفعة"""

    def __init__(self, parent=None, project_id=None, project_data=None, payment_id=None, edit_mode=False):
        super().__init__(parent)
        self.parent = parent
        self.project_id = project_id
        self.project_data = project_data or {}
        self.payment_id = payment_id
        self.edit_mode = edit_mode

        # إعداد النافذة
        self.setup_dialog()

        # إنشاء واجهة المستخدم
        self.create_dialog_ui()

        # تحميل البيانات إذا كان في وضع التعديل
        if self.edit_mode and self.payment_id:
            self.load_payment_data()

        # تطبيق الستايل
        apply_stylesheet(self)

    def setup_dialog(self):
        """إعداد النافذة"""
        title = "تعديل الدفعة" if self.edit_mode else "إضافة دفعة جديدة"
        self.setWindowTitle(title)
        self.setGeometry(200, 200, 600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # إعداد أيقونة النافذة
        app_icon_path = os.path.join(icons_dir, "icon_app.png")
        if os.path.exists(app_icon_path):
            self.setWindowIcon(QIcon(app_icon_path))

    def create_dialog_ui(self):
        """إنشاء واجهة المستخدم للنافذة"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("تعديل الدفعة" if self.edit_mode else "إضافة دفعة جديدة")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # قسم بيانات الدفعة
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
            }
        """)

        form_layout = QGridLayout(form_frame)

        # اختيار المشروع (إذا لم يكن محدد مسبقاً)
        if not self.project_id:
            form_layout.addWidget(QLabel("المشروع:"), 0, 0)
            self.project_combo = QComboBox()
            self.project_combo.setMinimumHeight(35)
            self.load_projects_list()
            self.project_combo.currentTextChanged.connect(self.on_project_changed)
            form_layout.addWidget(self.project_combo, 0, 1)

        # اسم العميل (للعرض فقط)
        form_layout.addWidget(QLabel("اسم العميل:"), 1, 0)
        self.client_name_label = QLabel(self.project_data.get('اسم_العميل', ''))
        self.client_name_label.setStyleSheet("font-weight: bold; color: #34495e; padding: 8px; background-color: #e9ecef; border-radius: 5px;")
        form_layout.addWidget(self.client_name_label, 1, 1)

        # مبلغ الدفعة
        form_layout.addWidget(QLabel("مبلغ الدفعة: *"), 2, 0)
        self.amount_edit = QLineEdit()
        self.amount_edit.setMinimumHeight(35)
        self.amount_edit.setPlaceholderText("أدخل مبلغ الدفعة")
        form_layout.addWidget(self.amount_edit, 2, 1)

        # وصف الدفعة
        form_layout.addWidget(QLabel("وصف الدفعة:"), 3, 0)
        self.description_edit = QLineEdit()
        self.description_edit.setMinimumHeight(35)
        self.description_edit.setPlaceholderText("وصف الدفعة (اختياري)")
        form_layout.addWidget(self.description_edit, 3, 1)

        # تاريخ الدفع
        form_layout.addWidget(QLabel("تاريخ الدفع: *"), 4, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setMinimumHeight(35)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDisplayFormat("dd/MM/yyyy")
        form_layout.addWidget(self.date_edit, 4, 1)

        # طريقة الدفع
        form_layout.addWidget(QLabel("طريقة الدفع: *"), 5, 0)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.setMinimumHeight(35)
        self.payment_method_combo.addItems([
            "نقدي", "تحويل بنكي", "شيك", "بطاقة ائتمان",
            "بطاقة مدين", "حوالة", "أخرى"
        ])
        form_layout.addWidget(self.payment_method_combo, 5, 1)

        # اسم المستلم
        form_layout.addWidget(QLabel("اسم المستلم:"), 6, 0)
        self.receiver_edit = QLineEdit()
        self.receiver_edit.setMinimumHeight(35)
        self.receiver_edit.setPlaceholderText("اسم الشخص المستلم للدفعة")
        form_layout.addWidget(self.receiver_edit, 6, 1)

        # ملاحظات إضافية
        form_layout.addWidget(QLabel("ملاحظات:"), 7, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية (اختياري)")
        form_layout.addWidget(self.notes_edit, 7, 1)

        main_layout.addWidget(form_frame)

        # قسم الأزرار
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)

        # زر الحفظ
        self.save_btn = QPushButton(qta.icon('fa5s.save', color='white'), "حفظ" if not self.edit_mode else "تحديث")
        self.save_btn.setMinimumSize(120, 40)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.save_btn.clicked.connect(self.save_payment)
        buttons_layout.addWidget(self.save_btn)

        # زر الإلغاء
        self.cancel_btn = QPushButton(qta.icon('fa5s.times', color='white'), "إلغاء")
        self.cancel_btn.setMinimumSize(120, 40)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        buttons_layout.addStretch()
        main_layout.addWidget(buttons_frame)

        # تطبيق التنقل بين الحقول
        focus_widgets = [
            self.amount_edit, self.description_edit, self.date_edit,
            self.payment_method_combo, self.receiver_edit, self.notes_edit
        ]

        if hasattr(self, 'project_combo'):
            focus_widgets.insert(0, self.project_combo)

        apply_enter_focus(self, "حفظ" if not self.edit_mode else "تحديث", focus_widgets)

        # تركيز على حقل المبلغ
        self.amount_edit.setFocus()
        self.amount_edit.selectAll()

    def load_projects_list(self):
        """تحميل قائمة المشاريع"""
        try:
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT p.id, p.اسم_المشروع, c.اسم_العميل, p.معرف_العميل
                FROM المشاريع p
                LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                ORDER BY p.اسم_المشروع
            """)

            projects = cursor.fetchall()
            conn.close()

            self.project_combo.clear()
            self.project_combo.addItem("اختر المشروع...", None)

            for project in projects:
                display_text = f"{project[1]} - {project[2]}"
                project_data = {
                    'id': project[0],
                    'اسم_المشروع': project[1],
                    'اسم_العميل': project[2],
                    'معرف_العميل': project[3]
                }
                self.project_combo.addItem(display_text, project_data)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل قائمة المشاريع:\n{str(e)}")

    def on_project_changed(self):
        """عند تغيير المشروع المحدد"""
        current_data = self.project_combo.currentData()
        if current_data:
            self.project_id = current_data['id']
            self.project_data = current_data
            self.client_name_label.setText(current_data['اسم_العميل'])

    def load_payment_data(self):
        """تحميل بيانات الدفعة للتعديل"""
        try:
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT dp.*, c.اسم_العميل, p.اسم_المشروع
                FROM المشاريع_المدفوعات dp
                LEFT JOIN المشاريع p ON dp.معرف_المشروع = p.id
                LEFT JOIN العملاء c ON p.معرف_العميل = c.id
                WHERE dp.id = %s
            """, (self.payment_id,))

            payment_data = cursor.fetchone()
            conn.close()

            if payment_data:
                # تعبئة الحقول بالبيانات الموجودة
                self.amount_edit.setText(str(float(payment_data[4])))
                self.description_edit.setText(payment_data[3] or "")

                # تحويل تاريخ الدفع
                if payment_data[5]:
                    payment_date = QDate.fromString(str(payment_data[5]), "yyyy-MM-dd")
                    self.date_edit.setDate(payment_date)

                # طريقة الدفع
                if payment_data[6]:
                    method_index = self.payment_method_combo.findText(payment_data[6])
                    if method_index >= 0:
                        self.payment_method_combo.setCurrentIndex(method_index)

                self.receiver_edit.setText(payment_data[7] or "")

                # تحديث معلومات العميل والمشروع
                self.client_name_label.setText(payment_data[13] or "")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الدفعة:\n{str(e)}")

    def validate_input(self):
        """التحقق من صحة البيانات المدخلة"""
        # التحقق من اختيار المشروع
        if not self.project_id:
            QMessageBox.warning(self, "خطأ في البيانات", "يرجى اختيار المشروع.")
            return False

        # التحقق من مبلغ الدفعة
        amount_text = self.amount_edit.text().strip()
        if not amount_text:
            QMessageBox.warning(self, "خطأ في البيانات", "يرجى إدخال مبلغ الدفعة.")
            self.amount_edit.setFocus()
            return False

        try:
            amount = float(amount_text)
            if amount <= 0:
                QMessageBox.warning(self, "خطأ في البيانات", "مبلغ الدفعة يجب أن يكون أكبر من الصفر.")
                self.amount_edit.setFocus()
                return False
        except ValueError:
            QMessageBox.warning(self, "خطأ في البيانات", "مبلغ الدفعة يجب أن يكون رقماً صحيحاً.")
            self.amount_edit.setFocus()
            return False

        # التحقق من طريقة الدفع
        if self.payment_method_combo.currentText() == "اختر طريقة الدفع...":
            QMessageBox.warning(self, "خطأ في البيانات", "يرجى اختيار طريقة الدفع.")
            return False

        return True

    def save_payment(self):
        """حفظ الدفعة"""
        if not self.validate_input():
            return

        try:
            # جمع البيانات
            amount = float(self.amount_edit.text().strip())
            description = self.description_edit.text().strip()
            payment_date = self.date_edit.date().toString("yyyy-MM-dd")
            payment_method = self.payment_method_combo.currentText()
            receiver = self.receiver_edit.text().strip()

            # الاتصال بقاعدة البيانات
            db_name = "project_manager_V2"
            conn = mysql.connector.connect(host=host, user=user, password=password, database=db_name)
            cursor = conn.cursor()

            if self.edit_mode:
                # تحديث الدفعة الموجودة
                cursor.execute("""
                    UPDATE المشاريع_المدفوعات
                    SET المبلغ_المدفوع = %s, وصف_المدفوع = %s, تاريخ_الدفع = %s,
                        طريقة_الدفع = %s, المستلم = %s
                    WHERE id = %s
                """, (amount, description, payment_date, payment_method, receiver, self.payment_id))

                success_message = f"تم تحديث الدفعة رقم {self.payment_id} بنجاح."
            else:
                # إضافة دفعة جديدة
                cursor.execute("""
                    INSERT INTO المشاريع_المدفوعات
                    (معرف_العميل, معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع, طريقة_الدفع, خصم, المستلم, المستخدم)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    self.project_data.get('معرف_العميل'),
                    self.project_id,
                    amount,
                    description,
                    payment_date,
                    payment_method,
                    0,  # خصم
                    receiver,
                    'النظام'  # المستخدم
                ))

                payment_id = cursor.lastrowid
                success_message = f"تم إضافة الدفعة رقم {payment_id} بنجاح."

            conn.commit()
            conn.close()

            # عرض رسالة النجاح
            QMessageBox.information(self, "نجاح", success_message)

            # إغلاق النافذة
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الدفعة:\n{str(e)}")
            print(f"خطأ في حفظ الدفعة: {e}")


class SearchFilterDialog(QDialog):
    """نافذة البحث والتصفية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setup_dialog()
        self.create_ui()
        apply_stylesheet(self)

    def setup_dialog(self):
        """إعداد النافذة"""
        self.setWindowTitle("البحث والتصفية")
        self.setGeometry(300, 300, 500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def create_ui(self):
        """إنشاء واجهة المستخدم"""
        main_layout = QVBoxLayout(self)

        # عنوان النافذة
        title_label = QLabel("البحث والتصفية في المدفوعات")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # قسم البحث
        search_frame = QFrame()
        search_frame.setFrameStyle(QFrame.StyledPanel)
        search_layout = QGridLayout(search_frame)

        # البحث بالوصف
        search_layout.addWidget(QLabel("البحث في الوصف:"), 0, 0)
        self.description_search = QLineEdit()
        self.description_search.setPlaceholderText("ابحث في وصف المدفوعات...")
        search_layout.addWidget(self.description_search, 0, 1)

        # البحث بالمبلغ
        search_layout.addWidget(QLabel("المبلغ من:"), 1, 0)
        self.amount_from = QLineEdit()
        self.amount_from.setPlaceholderText("الحد الأدنى للمبلغ")
        search_layout.addWidget(self.amount_from, 1, 1)

        search_layout.addWidget(QLabel("المبلغ إلى:"), 2, 0)
        self.amount_to = QLineEdit()
        self.amount_to.setPlaceholderText("الحد الأعلى للمبلغ")
        search_layout.addWidget(self.amount_to, 2, 1)

        # البحث بالتاريخ
        search_layout.addWidget(QLabel("من تاريخ:"), 3, 0)
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_from.setCalendarPopup(True)
        search_layout.addWidget(self.date_from, 3, 1)

        search_layout.addWidget(QLabel("إلى تاريخ:"), 4, 0)
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        search_layout.addWidget(self.date_to, 4, 1)

        # طريقة الدفع
        search_layout.addWidget(QLabel("طريقة الدفع:"), 5, 0)
        self.payment_method_filter = QComboBox()
        self.payment_method_filter.addItems([
            "جميع الطرق", "نقدي", "تحويل بنكي", "شيك",
            "بطاقة ائتمان", "بطاقة مدين", "حوالة", "أخرى"
        ])
        search_layout.addWidget(self.payment_method_filter, 5, 1)

        main_layout.addWidget(search_frame)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر البحث
        search_btn = QPushButton(qta.icon('fa5s.search', color='white'), "بحث")
        search_btn.setMinimumSize(100, 35)
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        search_btn.clicked.connect(self.perform_search)
        buttons_layout.addWidget(search_btn)

        # زر إعادة تعيين
        reset_btn = QPushButton(qta.icon('fa5s.undo', color='white'), "إعادة تعيين")
        reset_btn.setMinimumSize(100, 35)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        reset_btn.clicked.connect(self.reset_filters)
        buttons_layout.addWidget(reset_btn)

        # زر إغلاق
        close_btn = QPushButton(qta.icon('fa5s.times', color='white'), "إغلاق")
        close_btn.setMinimumSize(100, 35)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)

        main_layout.addLayout(buttons_layout)

    def perform_search(self):
        """تنفيذ البحث"""
        # هنا يمكن إضافة منطق البحث الفعلي
        QMessageBox.information(self, "البحث", "سيتم تطبيق معايير البحث المحددة.")
        self.close()

    def reset_filters(self):
        """إعادة تعيين المرشحات"""
        self.description_search.clear()
        self.amount_from.clear()
        self.amount_to.clear()
        self.date_from.setDate(QDate.currentDate().addMonths(-1))
        self.date_to.setDate(QDate.currentDate())
        self.payment_method_filter.setCurrentIndex(0)


# دوال مساعدة لفتح نوافذ إدارة المدفوعات

def open_payment_management_window(parent=None, project_id=None, project_data=None):
    """فتح نافذة إدارة المدفوعات"""
    try:
        window = PaymentManagementWindow(parent, project_id, project_data)
        window.show()
        return window
    except Exception as e:
        QMessageBox.critical(parent, "خطأ", f"فشل في فتح نافذة إدارة المدفوعات:\n{str(e)}")
        return None

def add_payment_dialog(parent=None, project_id=None, project_data=None):
    """فتح نافذة إضافة دفعة جديدة"""
    try:
        dialog = PaymentDialog(parent, project_id, project_data)
        return dialog.exec()
    except Exception as e:
        QMessageBox.critical(parent, "خطأ", f"فشل في فتح نافذة إضافة الدفعة:\n{str(e)}")
        return QDialog.Rejected

def edit_payment_dialog(parent=None, payment_id=None):
    """فتح نافذة تعديل دفعة موجودة"""
    try:
        dialog = PaymentDialog(parent, payment_id=payment_id, edit_mode=True)
        return dialog.exec()
    except Exception as e:
        QMessageBox.critical(parent, "خطأ", f"فشل في فتح نافذة تعديل الدفعة:\n{str(e)}")
        return QDialog.Rejected

# دالة للتكامل مع نظام البطاقات الموجود
def integrate_with_project_card(card_instance):
    """دمج نظام المدفوعات مع بطاقة المشروع"""
    try:
        project_data = card_instance.data
        project_id = project_data.get('id')

        if project_id:
            return open_payment_management_window(
                card_instance.parent(),
                project_id,
                project_data
            )
        else:
            QMessageBox.warning(
                card_instance.parent(),
                "خطأ",
                "معرف المشروع غير متوفر"
            )
            return None
    except Exception as e:
        QMessageBox.critical(
            card_instance.parent(),
            "خطأ",
            f"فشل في فتح نافذة المدفوعات:\n{str(e)}"
        )
        return None


if __name__ == "__main__":
    import sys
    from PySide6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # بيانات تجريبية للاختبار
    test_project_data = {
        'id': 1,
        'اسم_المشروع': 'مشروع تجريبي',
        'اسم_العميل': 'عميل تجريبي',
        'معرف_العميل': 1
    }

    window = PaymentManagementWindow(project_id=1, project_data=test_project_data)
    window.show()

    sys.exit(app.exec())
