#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة المدفوعات
Test script for the payment management system
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox

def test_payment_system():
    """اختبار نظام المدفوعات"""
    try:
        # استيراد نظام المدفوعات
        from المدفوعات import (
            PaymentManagementWindow, 
            PaymentDialog, 
            open_payment_management_window,
            add_payment_dialog
        )
        
        print("✅ تم استيراد نظام المدفوعات بنجاح")
        
        # بيانات تجريبية
        test_project_data = {
            'id': 1,
            'اسم_المشروع': 'مشروع تجريبي للاختبار',
            'اسم_العميل': 'عميل تجريبي',
            'معرف_العميل': 1
        }
        
        print("✅ تم إعداد البيانات التجريبية")
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ تم إنشاء التطبيق")
        
        # اختبار إنشاء نافذة إدارة المدفوعات
        try:
            payment_window = PaymentManagementWindow(
                project_id=1, 
                project_data=test_project_data
            )
            print("✅ تم إنشاء نافذة إدارة المدفوعات بنجاح")
            
            # اختبار إنشاء نافذة إضافة الدفعة
            payment_dialog = PaymentDialog(
                project_id=1, 
                project_data=test_project_data
            )
            print("✅ تم إنشاء نافذة إضافة الدفعة بنجاح")
            
            # عرض النافذة للاختبار البصري
            payment_window.show()
            print("✅ تم عرض نافذة إدارة المدفوعات")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النوافذ: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_integration():
    """اختبار التكامل مع النظام الموجود"""
    try:
        # اختبار التكامل مع نظام البطاقات
        print("🔄 اختبار التكامل مع نظام البطاقات...")
        
        # اختبار التكامل مع مراحل المشروع
        print("🔄 اختبار التكامل مع مراحل المشروع...")
        
        print("✅ اختبار التكامل مكتمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("🚀 بدء اختبار نظام إدارة المدفوعات")
    print("=" * 50)
    
    # اختبار النظام الأساسي
    if test_payment_system():
        print("✅ اختبار النظام الأساسي نجح")
    else:
        print("❌ اختبار النظام الأساسي فشل")
        return False
    
    # اختبار التكامل
    if test_integration():
        print("✅ اختبار التكامل نجح")
    else:
        print("❌ اختبار التكامل فشل")
        return False
    
    print("=" * 50)
    print("🎉 جميع الاختبارات نجحت!")
    print("=" * 50)
    
    # تشغيل التطبيق للاختبار البصري
    app = QApplication.instance()
    if app:
        print("📱 النافذة مفتوحة للاختبار البصري...")
        print("💡 يمكنك الآن اختبار الوظائف يدوياً")
        return app.exec()
    
    return True

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        sys.exit(1)
