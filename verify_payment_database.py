#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Verification script to check the current state of the payment database
and identify the exact cause of the 'اسم_العميل' column error.
"""

import mysql.connector
import sys
import os

def get_database_connection():
    """Get database connection"""
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from for_all import host, user, password
        
        return mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database="project_manager_V2"
        )
    except ImportError:
        print("⚠️  Could not import database settings")
        return None

def check_table_structure(cursor, table_name):
    """Check the structure of a table"""
    print(f"\n📋 Table: {table_name}")
    print("-" * 40)
    
    try:
        cursor.execute(f"DESCRIBE {table_name}")
        columns = cursor.fetchall()
        
        if columns:
            print("Columns:")
            for col in columns:
                print(f"  - {col[0]} ({col[1]})")
        else:
            print("❌ Table does not exist or has no columns")
            
    except mysql.connector.Error as e:
        print(f"❌ Error checking table: {e}")

def check_triggers(cursor):
    """Check all triggers in the database"""
    print(f"\n🔧 Database Triggers")
    print("-" * 40)
    
    cursor.execute("""
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE
        FROM INFORMATION_SCHEMA.TRIGGERS
        WHERE TRIGGER_SCHEMA = 'project_manager_V2'
        ORDER BY TRIGGER_NAME
    """)
    
    triggers = cursor.fetchall()
    
    if triggers:
        for trigger in triggers:
            print(f"  - {trigger[0]} ({trigger[1]} on {trigger[2]})")
    else:
        print("ℹ️  No triggers found")

def check_trigger_definition(cursor, trigger_name):
    """Check the definition of a specific trigger"""
    print(f"\n🔍 Trigger Definition: {trigger_name}")
    print("-" * 50)
    
    cursor.execute("""
        SELECT ACTION_STATEMENT
        FROM INFORMATION_SCHEMA.TRIGGERS
        WHERE TRIGGER_SCHEMA = 'project_manager_V2'
        AND TRIGGER_NAME = %s
    """, (trigger_name,))
    
    result = cursor.fetchone()
    if result:
        print(result[0])
    else:
        print(f"❌ Trigger '{trigger_name}' not found")

def test_payment_insert(cursor):
    """Test a simple payment insertion"""
    print(f"\n🧪 Testing Payment Insertion")
    print("-" * 40)
    
    try:
        # First check if we have any projects to reference
        cursor.execute("SELECT id FROM المشاريع LIMIT 1")
        project = cursor.fetchone()
        
        if not project:
            print("⚠️  No projects found - creating test project first")
            
            # Check if we have clients
            cursor.execute("SELECT id FROM العملاء LIMIT 1")
            client = cursor.fetchone()
            
            if not client:
                print("⚠️  No clients found - creating test client first")
                cursor.execute("""
                    INSERT INTO العملاء (اسم_العميل, العنوان, رقم_الهاتف, تاريخ_الإضافة)
                    VALUES ('عميل تجريبي', 'عنوان تجريبي', '123456789', CURDATE())
                """)
                client_id = cursor.lastrowid
                print(f"✅ Created test client with ID: {client_id}")
            else:
                client_id = client[0]
                print(f"✅ Using existing client ID: {client_id}")
            
            # Create test project
            cursor.execute("""
                INSERT INTO المشاريع (معرف_العميل, اسم_المشروع, المبلغ, تاريخ_الإستلام)
                VALUES (%s, 'مشروع تجريبي', 10000.00, CURDATE())
            """, (client_id,))
            project_id = cursor.lastrowid
            print(f"✅ Created test project with ID: {project_id}")
        else:
            project_id = project[0]
            print(f"✅ Using existing project ID: {project_id}")
            
            # Get client ID for this project
            cursor.execute("SELECT معرف_العميل FROM المشاريع WHERE id = %s", (project_id,))
            client_result = cursor.fetchone()
            client_id = client_result[0] if client_result else 1
        
        # Now try to insert a payment
        print("💰 Attempting payment insertion...")
        
        cursor.execute("""
            INSERT INTO المشاريع_المدفوعات 
            (معرف_العميل, معرف_المشروع, المبلغ_المدفوع, وصف_المدفوع, تاريخ_الدفع, طريقة_الدفع, خصم, المستلم, المستخدم) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            client_id,
            project_id,
            500.00,
            'دفعة تجريبية',
            '2024-01-01',
            'نقدي',
            0,
            'مستلم تجريبي',
            'النظام'
        ))
        
        payment_id = cursor.lastrowid
        print(f"✅ Payment inserted successfully with ID: {payment_id}")
        
        # Clean up test payment
        cursor.execute("DELETE FROM المشاريع_المدفوعات WHERE id = %s", (payment_id,))
        print("🧹 Test payment cleaned up")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ Payment insertion failed: {e}")
        print(f"   Error Code: {e.errno}")
        print(f"   SQL State: {e.sqlstate}")
        return False

def main():
    """Main verification function"""
    print("=" * 60)
    print("🔍 PAYMENT DATABASE VERIFICATION")
    print("=" * 60)
    
    conn = get_database_connection()
    if not conn:
        print("❌ Could not connect to database")
        return 1
    
    cursor = conn.cursor()
    
    try:
        # Check main tables structure
        tables_to_check = [
            'المشاريع_المدفوعات',
            'المشاريع',
            'العملاء',
            'المشاريع_المراحل',
            'المقاولات_العهد',
            'الموظفين_معاملات_مالية'
        ]
        
        for table in tables_to_check:
            check_table_structure(cursor, table)
        
        # Check triggers
        check_triggers(cursor)
        
        # Check specific problematic triggers
        problematic_triggers = ['تحديث_التصميم', 'update_custody_project_info']
        for trigger in problematic_triggers:
            check_trigger_definition(cursor, trigger)
        
        # Test payment insertion
        test_success = test_payment_insert(cursor)
        
        # Rollback any test changes
        conn.rollback()
        
        print("\n" + "=" * 60)
        if test_success:
            print("✅ VERIFICATION PASSED - No issues found")
        else:
            print("❌ VERIFICATION FAILED - Issues detected")
            print("   Run fix_payment_database_error.py to fix the issues")
        print("=" * 60)
        
        return 0 if test_success else 1
        
    except Exception as e:
        print(f"\n💥 Verification error: {e}")
        return 1
    finally:
        conn.close()

if __name__ == "__main__":
    sys.exit(main())
